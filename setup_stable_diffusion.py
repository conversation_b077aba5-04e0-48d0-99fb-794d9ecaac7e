#!/usr/bin/env python3
"""
Script setup Stable Diffusion environment
"""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path

def run_command(cmd, description=""):
    """Ch<PERSON>y lệnh và hiển thị kết quả"""
    print(f"🔄 {description}")
    print(f"   Lệnh: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    
    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Lỗi!")
        print(f"   Return code: {e.returncode}")
        print(f"   STDERR: {e.stderr}")
        return False

def check_python_packages():
    """Kiểm tra và cài đặt Python packages"""
    print("📦 Kiểm tra Python packages...")
    
    required_packages = [
        "torch",
        "torchvision", 
        "numpy",
        "pillow",
        "opencv-python",
        "transformers",
        "diffusers",
        "omegaconf",
        "pytorch-lightning",
        "einops"
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} đã được cài đặt")
        except ImportError:
            print(f"⚠️  {package} chưa được cài đặt, đang cài...")
            if not run_command([sys.executable, "-m", "pip", "install", package], f"Cài đặt {package}"):
                return False
    
    return True

def setup_model_directory():
    """Tạo thư mục model và tải checkpoint"""
    print("📁 Thiết lập thư mục model...")
    
    model_dir = Path("home-improvement/stable-diffusion-optimized/models/ldm/stable-diffusion-v1")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    model_path = model_dir / "model.ckpt"
    
    if model_path.exists():
        print(f"✅ Model checkpoint đã tồn tại: {model_path}")
        return True
    
    print("⚠️  Model checkpoint chưa có.")
    print("📥 Bạn cần tải model checkpoint từ:")
    print("   - Hugging Face: https://huggingface.co/runwayml/stable-diffusion-v1-5")
    print("   - Hoặc từ nguồn khác")
    print(f"   Và đặt vào: {model_path}")
    
    # Thử tải model từ Hugging Face (nếu có git-lfs)
    print("🔄 Thử tải model từ Hugging Face...")
    
    try:
        # Kiểm tra git-lfs
        subprocess.run(["git", "lfs", "--version"], check=True, capture_output=True)
        
        # Clone repo
        clone_cmd = [
            "git", "clone", 
            "https://huggingface.co/runwayml/stable-diffusion-v1-5",
            str(model_dir.parent / "temp_model")
        ]
        
        if run_command(clone_cmd, "Clone model từ Hugging Face"):
            # Copy model file
            temp_model = model_dir.parent / "temp_model" / "v1-5-pruned.ckpt"
            if temp_model.exists():
                import shutil
                shutil.copy2(temp_model, model_path)
                print(f"✅ Đã copy model đến: {model_path}")
                
                # Cleanup
                shutil.rmtree(model_dir.parent / "temp_model")
                return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Không thể tự động tải model. Vui lòng tải thủ công.")
    
    return False

def create_demo_script():
    """Tạo script demo"""
    print("📝 Tạo script demo...")
    
    demo_script = """#!/usr/bin/env python3
# Demo script để test Stable Diffusion

import os
import sys

def main():
    print("🎨 Stable Diffusion Demo")
    print("=" * 50)
    
    # Kiểm tra model
    model_path = "home-improvement/stable-diffusion-optimized/models/ldm/stable-diffusion-v1/model.ckpt"
    if os.path.exists(model_path):
        print("✅ Model checkpoint có sẵn")
    else:
        print("❌ Model checkpoint không tìm thấy")
        print(f"   Cần đặt model tại: {model_path}")
        return 1
    
    # Kiểm tra ảnh input
    input_image = "z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    if os.path.exists(input_image):
        print(f"✅ Ảnh input có sẵn: {input_image}")
    else:
        print(f"❌ Ảnh input không tìm thấy: {input_image}")
        return 1
    
    print()
    print("🚀 Để chạy convert ảnh, sử dụng:")
    print(f"python convert_image.py -i {input_image} -p 'beautiful modern house exterior'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
"""
    
    with open("demo.py", "w", encoding="utf-8") as f:
        f.write(demo_script)
    
    print("✅ Đã tạo demo.py")

def main():
    print("🎨 Stable Diffusion Setup")
    print("=" * 50)
    
    # Kiểm tra Python
    print(f"🐍 Python version: {sys.version}")
    
    # Kiểm tra thư mục
    if not os.path.exists("home-improvement/stable-diffusion-optimized"):
        print("❌ Không tìm thấy thư mục stable-diffusion-optimized")
        print("   Hãy đảm bảo bạn đang ở đúng thư mục workspace")
        return 1
    
    # Cài đặt packages
    if not check_python_packages():
        print("❌ Lỗi khi cài đặt Python packages")
        return 1
    
    # Setup model
    setup_model_directory()
    
    # Tạo demo script
    create_demo_script()
    
    print()
    print("🎉 Setup hoàn tất!")
    print("📋 Các bước tiếp theo:")
    print("1. Chạy: python demo.py để kiểm tra")
    print("2. Nếu thiếu model, tải từ Hugging Face")
    print("3. Chạy: python convert_image.py để convert ảnh")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
