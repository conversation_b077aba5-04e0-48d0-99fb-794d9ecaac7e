#!/usr/bin/env python3
"""
Demo nhanh tạo ảnh nghệ thuật từ ảnh gốc
Sử dụng diffusers với model nhẹ hơn
"""

import os
import sys
import argparse
from PIL import Image
import torch

def main():
    parser = argparse.ArgumentParser(description='Demo nhanh tạo ảnh nghệ thuật')
    parser.add_argument('--input', '-i', default='home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg', help='Ảnh input')
    parser.add_argument('--style', '-s', choices=['fantasy', 'artistic', 'sketch', 'modern'], default='fantasy', help='Style nghệ thuật')
    parser.add_argument('--strength', type=float, default=0.7, help='Strength (0.0-1.0)')
    parser.add_argument('--output', '-o', default='artistic_output.jpg', help='File output')
    
    args = parser.parse_args()
    
    # Kiểm tra file input
    if not os.path.exists(args.input):
        print(f"❌ Không tìm thấy file: {args.input}")
        return 1
    
    print("🎨 Quick Artistic Demo")
    print("=" * 40)
    print(f"📁 Input: {args.input}")
    print(f"🎭 Style: {args.style}")
    print(f"💪 Strength: {args.strength}")
    print(f"💾 Output: {args.output}")
    print()
    
    # Định nghĩa prompts cho từng style
    style_prompts = {
        'fantasy': "A fantasy english family home, beautiful architecture, fantasy illustration, trending on artstation, detailed, magical lighting, fairy tale style",
        'artistic': "Beautiful house exterior design, artistic painting style, watercolor illustration, soft colors, architectural drawing, professional art",
        'sketch': "House exterior architectural sketch, pencil drawing style, detailed lineart, professional architectural illustration, black and white",
        'modern': "Modern house exterior concept art, professional architectural visualization, clean lines, contemporary design, trending on artstation"
    }
    
    prompt = style_prompts[args.style]
    print(f"📝 Prompt: {prompt}")
    print()
    
    try:
        print("📦 Loading libraries...")
        from diffusers import StableDiffusionImg2ImgPipeline
        
        # Kiểm tra device
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️  Device: {device}")
        
        if device == "cpu":
            print("⚠️  Sử dụng CPU, quá trình sẽ chậm hơn")
        
        # Load pipeline với model nhẹ hơn
        print("🔄 Loading Stable Diffusion model...")
        print("   (Lần đầu sẽ tải model từ internet, có thể mất vài phút)")
        
        # Sử dụng model nhỏ hơn để demo nhanh
        model_id = "runwayml/stable-diffusion-v1-5"
        
        pipe = StableDiffusionImg2ImgPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            safety_checker=None,
            requires_safety_checker=False
        )
        pipe = pipe.to(device)
        
        # Optimize memory
        if device == "cuda":
            pipe.enable_attention_slicing()
            pipe.enable_model_cpu_offload()
        
        print("✅ Model loaded!")
        
        # Load và resize ảnh
        print("🖼️  Processing input image...")
        init_image = Image.open(args.input).convert("RGB")
        
        # Resize để tối ưu tốc độ (512x512)
        width, height = init_image.size
        print(f"   Original size: {width}x{height}")
        
        # Tính toán kích thước mới giữ tỷ lệ
        max_size = 512
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)
        
        # Đảm bảo chia hết cho 8
        new_width = (new_width // 8) * 8
        new_height = (new_height // 8) * 8
        
        init_image = init_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        print(f"   Resized to: {new_width}x{new_height}")
        
        # Generate
        print("🎨 Generating artistic image...")
        print("   (Có thể mất 1-5 phút tùy vào device)")
        
        generator = torch.Generator(device=device).manual_seed(42)
        
        with torch.autocast(device):
            result = pipe(
                prompt=prompt,
                image=init_image,
                strength=args.strength,
                num_inference_steps=20,  # Giảm steps để nhanh hơn
                guidance_scale=7.5,
                generator=generator
            )
        
        # Save result
        output_image = result.images[0]
        output_image.save(args.output)
        
        print(f"✅ Hoàn thành! Ảnh nghệ thuật đã được lưu: {args.output}")
        
        # Hiển thị thông tin
        print()
        print("📊 Kết quả:")
        print(f"   Input: {width}x{height}")
        print(f"   Output: {output_image.size}")
        print(f"   Style: {args.style}")
        print(f"   Strength: {args.strength}")
        
        if os.path.exists(args.output):
            size_mb = os.path.getsize(args.output) / (1024 * 1024)
            print(f"   File size: {size_mb:.2f} MB")
        
        print()
        print("🎉 Demo hoàn thành!")
        print("💡 Thử các style khác:")
        print("   python quick_artistic_demo.py --style fantasy")
        print("   python quick_artistic_demo.py --style artistic")
        print("   python quick_artistic_demo.py --style sketch")
        print("   python quick_artistic_demo.py --style modern")
        
        return 0
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        print("💡 Cài đặt: pip install diffusers transformers accelerate")
        return 1
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        print("💡 Thử lại hoặc kiểm tra kết nối internet")
        return 1

if __name__ == "__main__":
    sys.exit(main())
