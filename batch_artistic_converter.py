#!/usr/bin/env python3
"""
Script tạo nhiều phiên bản nghệ thuật cùng lúc
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_conversion(style, strength, output_suffix=""):
    """Chạy conversion cho một style"""
    
    input_img = "home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    output_name = f"artistic_{style}_{strength}{output_suffix}.jpg"
    
    cmd = [
        "python", "quick_artistic_demo.py",
        "--input", input_img,
        "--style", style,
        "--strength", str(strength),
        "--output", output_name
    ]
    
    print(f"🎨 Tạo {style} style với strength {strength}...")
    print(f"   Output: {output_name}")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        end_time = time.time()
        
        print(f"   ✅ <PERSON>àn thành trong {end_time - start_time:.1f}s")
        return True, output_name
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Lỗi: {e.returncode}")
        print(f"   STDERR: {e.stderr}")
        return False, None

def main():
    print("🎨 Batch Artistic Converter")
    print("=" * 50)
    print("Tạo nhiều phiên bản nghệ thuật từ ảnh gốc")
    print()
    
    # Kiểm tra file input
    input_img = "home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    if not os.path.exists(input_img):
        print(f"❌ Không tìm thấy file: {input_img}")
        return 1
    
    # Các cấu hình để test
    configs = [
        {"style": "fantasy", "strength": 0.6},
        {"style": "fantasy", "strength": 0.8},
        {"style": "artistic", "strength": 0.7},
        {"style": "sketch", "strength": 0.8},
        {"style": "modern", "strength": 0.5},
    ]
    
    print(f"📋 Sẽ tạo {len(configs)} phiên bản:")
    for i, config in enumerate(configs, 1):
        print(f"   {i}. {config['style']} (strength: {config['strength']})")
    print()
    
    # Tạo thư mục output
    output_dir = Path("artistic_outputs")
    output_dir.mkdir(exist_ok=True)
    
    successful = []
    failed = []
    
    total_start = time.time()
    
    for i, config in enumerate(configs, 1):
        print(f"[{i}/{len(configs)}] " + "="*40)
        
        success, output_file = run_conversion(
            config["style"], 
            config["strength"],
            f"_v{i}"
        )
        
        if success and output_file:
            # Di chuyển file vào thư mục output
            if os.path.exists(output_file):
                new_path = output_dir / output_file
                os.rename(output_file, new_path)
                successful.append(str(new_path))
                print(f"   📁 Moved to: {new_path}")
        else:
            failed.append(f"{config['style']} (strength: {config['strength']})")
        
        print()
    
    total_time = time.time() - total_start
    
    # Báo cáo kết quả
    print("🎉 HOÀN THÀNH!")
    print("=" * 50)
    print(f"⏱️  Tổng thời gian: {total_time/60:.1f} phút")
    print(f"✅ Thành công: {len(successful)}/{len(configs)}")
    print(f"❌ Thất bại: {len(failed)}")
    print()
    
    if successful:
        print("📂 Các file đã tạo:")
        for file in successful:
            if os.path.exists(file):
                size_mb = os.path.getsize(file) / (1024 * 1024)
                print(f"   • {file} ({size_mb:.2f} MB)")
    
    if failed:
        print("❌ Các conversion thất bại:")
        for item in failed:
            print(f"   • {item}")
    
    print()
    print("💡 Mẹo:")
    print("   - Xem kết quả trong thư mục 'artistic_outputs'")
    print("   - So sánh các strength khác nhau")
    print("   - Thử chạy lại với GPU để nhanh hơn")
    
    return 0 if len(successful) > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
