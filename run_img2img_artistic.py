#!/usr/bin/env python3
"""
Script chạy img2img theo hướng dẫn GitHub để tạo ảnh nghệ thuật
Dựa trên: https://github.com/lukexyz/home-improvement
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_img2img_artistic():
    """Chạy img2img với các prompt nghệ thuật khác nhau"""
    
    # Đường dẫn ảnh input
    input_img = "home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    
    # Kiểm tra file input
    if not os.path.exists(input_img):
        print(f"❌ Không tìm thấy file: {input_img}")
        return 1
    
    # Đường dẫn script optimized img2img
    script_path = "home-improvement/stable-diffusion-optimized/optimizedSD/optimized_img2img.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Không tìm thấy script: {script_path}")
        print("H<PERSON>y đảm bảo bạn đã có thư mục stable-diffusion-optimized")
        return 1
    
    # Tạo thư mục output
    outdir = "outputs/artistic_house"
    os.makedirs(outdir, exist_ok=True)
    
    # Các prompt nghệ thuật khác nhau
    prompts = [
        {
            "name": "fantasy_illustration",
            "prompt": "A fantasy english family home, beautiful architecture, fantasy illustration, trending on artstation, detailed, magical lighting",
            "strength": 0.6
        },
        {
            "name": "artistic_painting", 
            "prompt": "Beautiful house exterior design, artistic painting style, watercolor illustration, soft colors, architectural drawing",
            "strength": 0.7
        },
        {
            "name": "concept_art",
            "prompt": "Modern house exterior concept art, professional architectural visualization, clean lines, contemporary design, trending on artstation",
            "strength": 0.5
        },
        {
            "name": "sketch_style",
            "prompt": "House exterior architectural sketch, pencil drawing style, detailed lineart, professional architectural illustration",
            "strength": 0.8
        }
    ]
    
    print("🎨 Artistic House Conversion")
    print("=" * 50)
    print(f"📁 Input: {input_img}")
    print(f"📂 Output: {outdir}")
    print(f"🎭 Styles: {len(prompts)} different artistic styles")
    print()
    
    success_count = 0
    
    for i, prompt_config in enumerate(prompts, 1):
        print(f"🎨 [{i}/{len(prompts)}] Generating {prompt_config['name']}...")
        print(f"   Prompt: {prompt_config['prompt']}")
        print(f"   Strength: {prompt_config['strength']}")
        
        # Tạo lệnh chạy
        cmd = [
            "python", script_path,
            "--prompt", prompt_config['prompt'],
            "--init-img", input_img,
            "--strength", str(prompt_config['strength']),
            "--seed", str(200 + i),  # Seed khác nhau cho mỗi style
            "--outdir", f"{outdir}/{prompt_config['name']}",
            "--n_samples", "2",  # Tạo 2 ảnh cho mỗi style
            "--ddim_steps", "50",
            "--H", "512",
            "--W", "512"
        ]
        
        try:
            # Chạy lệnh
            print(f"   🔄 Running: {' '.join(cmd[:3])}...")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"   ✅ Success! Saved to {outdir}/{prompt_config['name']}")
            success_count += 1
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e.returncode}")
            print(f"   STDERR: {e.stderr}")
            
        except FileNotFoundError:
            print(f"   ❌ Python hoặc script không tìm thấy")
            
        print()
    
    print("🎉 Hoàn thành!")
    print(f"✅ Thành công: {success_count}/{len(prompts)} styles")
    print(f"📂 Kết quả trong: {outdir}")
    
    return 0 if success_count > 0 else 1

def run_strength_variations():
    """Chạy với các strength khác nhau như trong hướng dẫn"""
    
    input_img = "home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    script_path = "home-improvement/stable-diffusion-optimized/optimizedSD/optimized_img2img.py"
    
    if not os.path.exists(input_img) or not os.path.exists(script_path):
        print("❌ Thiếu file cần thiết")
        return 1
    
    # Prompt chính từ hướng dẫn
    pstring = "A fantasy english family home, beautiful architecture, fantasy illustration, trending on artstation"
    
    # Tạo thư mục output
    outdir = "outputs/strength_variations"
    os.makedirs(outdir, exist_ok=True)
    
    print("💪 Strength Variations")
    print("=" * 40)
    print(f"📝 Prompt: {pstring}")
    print()
    
    # Các strength từ 30% đến 75% (như trong hướng dẫn)
    strengths = range(30, 80, 10)  # 30, 40, 50, 60, 70
    
    for strength in strengths:
        strength_val = strength * 0.01
        print(f"💪 Testing strength: {strength_val}")
        
        cmd = [
            "python", script_path,
            "--prompt", pstring,
            "--init-img", input_img,
            "--strength", str(strength_val),
            "--seed", "200",
            "--outdir", f"{outdir}/strength_{strength}",
            "--n_samples", "1",
            "--ddim_steps", "50"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"   ✅ Success!")
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e.returncode}")
        
        print()
    
    print(f"📂 Kết quả trong: {outdir}")
    return 0

def main():
    parser = argparse.ArgumentParser(description='Chạy img2img artistic conversion')
    parser.add_argument('--mode', choices=['artistic', 'strength'], default='artistic',
                       help='Chế độ: artistic (nhiều style) hoặc strength (test strength)')
    
    args = parser.parse_args()
    
    if args.mode == 'artistic':
        return run_img2img_artistic()
    else:
        return run_strength_variations()

if __name__ == "__main__":
    sys.exit(main())
