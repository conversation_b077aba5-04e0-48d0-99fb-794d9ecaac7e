#!/usr/bin/env python3
"""
Script để convert ảnh bằng Stable Diffusion img2img
Sử dụng optimized version để tiết kiệm VRAM
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='Convert ảnh bằng Stable Diffusion img2img')
    parser.add_argument('--input', '-i', required=True, help='Đường dẫn ảnh input')
    parser.add_argument('--prompt', '-p', default='a beautiful house exterior design, modern architecture, professional photography', help='Prompt mô tả ảnh muốn tạo')
    parser.add_argument('--strength', '-s', type=float, default=0.7, help='Strength (0.0-1.0), càng cao càng khác ảnh gốc')
    parser.add_argument('--steps', type=int, default=50, help='Số bước sampling')
    parser.add_argument('--samples', type=int, default=3, help='Số ảnh tạo ra')
    parser.add_argument('--seed', type=int, default=42, help='Seed cho reproducible results')
    parser.add_argument('--output', '-o', default='outputs/converted', help='Thư mục output')
    
    args = parser.parse_args()
    
    # Kiểm tra file input
    if not os.path.exists(args.input):
        print(f"❌ Không tìm thấy file: {args.input}")
        return 1
    
    # Tạo thư mục output
    os.makedirs(args.output, exist_ok=True)
    
    # Đường dẫn đến script optimized img2img
    script_path = "home-improvement/stable-diffusion-optimized/optimizedSD/optimized_img2img.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Không tìm thấy script: {script_path}")
        print("Hãy đảm bảo bạn đã clone repo stable-diffusion-optimized")
        return 1
    
    # Tạo lệnh chạy
    cmd = [
        "python", script_path,
        "--prompt", args.prompt,
        "--init-img", args.input,
        "--strength", str(args.strength),
        "--ddim_steps", str(args.steps),
        "--n_samples", str(args.samples),
        "--seed", str(args.seed),
        "--outdir", args.output,
        "--H", "512",
        "--W", "512"
    ]
    
    print("🚀 Bắt đầu convert ảnh...")
    print(f"📁 Input: {args.input}")
    print(f"📝 Prompt: {args.prompt}")
    print(f"💪 Strength: {args.strength}")
    print(f"🎯 Steps: {args.steps}")
    print(f"🔢 Samples: {args.samples}")
    print(f"🌱 Seed: {args.seed}")
    print(f"📂 Output: {args.output}")
    print()
    print("⚡ Lệnh chạy:")
    print(" ".join(cmd))
    print()
    
    try:
        # Chạy lệnh
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Convert thành công!")
        print(f"📂 Kết quả được lưu trong: {args.output}")
        
    except subprocess.CalledProcessError as e:
        print("❌ Lỗi khi chạy script:")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return 1
    
    except FileNotFoundError:
        print("❌ Không tìm thấy Python hoặc script")
        print("Hãy đảm bảo:")
        print("1. Python đã được cài đặt")
        print("2. Đã cài đặt các dependencies cần thiết")
        print("3. Đã tải model checkpoint")
        return 1

if __name__ == "__main__":
    sys.exit(main())
