#!/usr/bin/env python3
"""
Script convert ảnh bằng Diffusers library (dễ sử dụng hơn)
"""

import os
import sys
import argparse
from PIL import Image
import torch

def main():
    parser = argparse.ArgumentParser(description='Convert ảnh bằng Stable Diffusion (Diffusers)')
    parser.add_argument('--input', '-i', required=True, help='Đường dẫn ảnh input')
    parser.add_argument('--prompt', '-p', default='a beautiful modern house exterior, professional architecture photography, high quality', help='Prompt mô tả ảnh muốn tạo')
    parser.add_argument('--strength', '-s', type=float, default=0.7, help='Strength (0.0-1.0), càng cao càng khác ảnh gốc')
    parser.add_argument('--steps', type=int, default=20, help='Số bước sampling')
    parser.add_argument('--guidance', type=float, default=7.5, help='Guidance scale')
    parser.add_argument('--seed', type=int, default=42, help='Seed cho reproducible results')
    parser.add_argument('--output', '-o', default='output_converted.jpg', help='File output')
    
    args = parser.parse_args()
    
    # Kiểm tra file input
    if not os.path.exists(args.input):
        print(f"❌ Không tìm thấy file: {args.input}")
        return 1
    
    print("🎨 Stable Diffusion Image-to-Image Converter")
    print("=" * 50)
    print(f"📁 Input: {args.input}")
    print(f"📝 Prompt: {args.prompt}")
    print(f"💪 Strength: {args.strength}")
    print(f"🎯 Steps: {args.steps}")
    print(f"🧭 Guidance: {args.guidance}")
    print(f"🌱 Seed: {args.seed}")
    print(f"💾 Output: {args.output}")
    print()
    
    try:
        # Import diffusers
        print("📦 Loading diffusers library...")
        from diffusers import StableDiffusionImg2ImgPipeline
        
        # Kiểm tra CUDA
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️  Device: {device}")
        
        if device == "cpu":
            print("⚠️  Đang sử dụng CPU, quá trình sẽ chậm hơn")
        
        # Load pipeline
        print("🔄 Loading Stable Diffusion model...")
        pipe = StableDiffusionImg2ImgPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            safety_checker=None,
            requires_safety_checker=False
        )
        pipe = pipe.to(device)
        
        # Optimize memory
        if device == "cuda":
            pipe.enable_attention_slicing()
            pipe.enable_model_cpu_offload()
        
        print("✅ Model loaded successfully!")
        
        # Load và resize ảnh input
        print("🖼️  Loading input image...")
        init_image = Image.open(args.input).convert("RGB")
        
        # Resize để phù hợp với model (512x512 hoặc tỷ lệ tương tự)
        width, height = init_image.size
        max_size = 512
        
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)
        
        # Đảm bảo kích thước chia hết cho 8 (yêu cầu của model)
        new_width = (new_width // 8) * 8
        new_height = (new_height // 8) * 8
        
        init_image = init_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        print(f"📐 Resized to: {new_width}x{new_height}")
        
        # Set seed
        generator = torch.Generator(device=device).manual_seed(args.seed)
        
        # Generate
        print("🎨 Generating image...")
        with torch.autocast(device):
            result = pipe(
                prompt=args.prompt,
                image=init_image,
                strength=args.strength,
                num_inference_steps=args.steps,
                guidance_scale=args.guidance,
                generator=generator
            )
        
        # Save result
        output_image = result.images[0]
        output_image.save(args.output)
        
        print(f"✅ Hoàn thành! Ảnh đã được lưu: {args.output}")
        
        # Hiển thị thông tin
        print()
        print("📊 Thông tin:")
        print(f"   Original size: {width}x{height}")
        print(f"   Processed size: {new_width}x{new_height}")
        print(f"   Output size: {output_image.size}")
        
        return 0
        
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        print("💡 Hãy cài đặt: pip install diffusers transformers accelerate")
        return 1
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
