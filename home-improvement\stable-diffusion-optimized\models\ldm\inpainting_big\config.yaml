model:
  base_learning_rate: 1.0e-06
  target: ldm.models.diffusion.ddpm.LatentDiffusion
  params:
    linear_start: 0.0015
    linear_end: 0.0205
    log_every_t: 100
    timesteps: 1000
    loss_type: l1
    first_stage_key: image
    cond_stage_key: masked_image
    image_size: 64
    channels: 3
    concat_mode: true
    monitor: val/loss
    scheduler_config:
      target: ldm.lr_scheduler.LambdaWarmUpCosineScheduler
      params:
        verbosity_interval: 0
        warm_up_steps: 1000
        max_decay_steps: 50000
        lr_start: 0.001
        lr_max: 0.1
        lr_min: 0.0001
    unet_config:
      target: ldm.modules.diffusionmodules.openaimodel.UNetModel
      params:
        image_size: 64
        in_channels: 7
        out_channels: 3
        model_channels: 256
        attention_resolutions:
        - 8
        - 4
        - 2
        num_res_blocks: 2
        channel_mult:
        - 1
        - 2
        - 3
        - 4
        num_heads: 8
        resblock_updown: true
    first_stage_config:
      target: ldm.models.autoencoder.VQModelInterface
      params:
        embed_dim: 3
        n_embed: 8192
        monitor: val/rec_loss
        ddconfig:
          attn_type: none
          double_z: false
          z_channels: 3
          resolution: 256
          in_channels: 3
          out_ch: 3
          ch: 128
          ch_mult:
          - 1
          - 2
          - 4
          num_res_blocks: 2
          attn_resolutions: []
          dropout: 0.0
        lossconfig:
          target: ldm.modules.losses.contperceptual.DummyLoss
    cond_stage_config: __is_first_stage__
