#!/usr/bin/env python3
"""
Script tải model Stable Diffusion từ Hugging Face
"""

import os
import sys
import urllib.request
from pathlib import Path

def download_file(url, filepath, description=""):
    """Tải file với progress bar"""
    print(f"📥 Downloading {description}...")
    print(f"   URL: {url}")
    print(f"   Destination: {filepath}")
    
    def progress_hook(block_num, block_size, total_size):
        if total_size > 0:
            percent = min(100, (block_num * block_size * 100) // total_size)
            print(f"\r   Progress: {percent}% ({block_num * block_size // (1024*1024)} MB / {total_size // (1024*1024)} MB)", end="")
    
    try:
        urllib.request.urlretrieve(url, filepath, progress_hook)
        print(f"\n   ✅ Downloaded successfully!")
        return True
    except Exception as e:
        print(f"\n   ❌ Error: {e}")
        return False

def main():
    print("🤗 Stable Diffusion Model Downloader")
    print("=" * 50)
    
    # Tạo thư mục model
    model_dir = Path("home-improvement/stable-diffusion-optimized/models/ldm/stable-diffusion-v1")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Đường dẫn model checkpoint
    model_path = model_dir / "model.ckpt"
    
    if model_path.exists():
        print(f"✅ Model đã tồn tại: {model_path}")
        size_mb = model_path.stat().st_size / (1024 * 1024)
        print(f"   Size: {size_mb:.1f} MB")
        return 0
    
    print("⚠️  Model chưa có, cần tải về...")
    print()
    
    # URL model từ Hugging Face (direct download link)
    model_urls = [
        {
            "name": "Stable Diffusion v1.4",
            "url": "https://huggingface.co/CompVis/stable-diffusion-v-1-4-original/resolve/main/sd-v1-4.ckpt",
            "filename": "model.ckpt"
        },
        {
            "name": "Stable Diffusion v1.5 (pruned)",
            "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned.ckpt", 
            "filename": "model.ckpt"
        }
    ]
    
    print("📋 Available models:")
    for i, model in enumerate(model_urls, 1):
        print(f"   {i}. {model['name']}")
    
    print()
    choice = input("Chọn model (1-2) hoặc Enter để dùng v1.5: ").strip()
    
    if choice == "1":
        selected_model = model_urls[0]
    else:
        selected_model = model_urls[1]  # Default v1.5
    
    print(f"🎯 Selected: {selected_model['name']}")
    print()
    
    # Tải model
    model_file = model_dir / selected_model['filename']
    
    if download_file(selected_model['url'], model_file, selected_model['name']):
        print()
        print("🎉 Model tải thành công!")
        print(f"📁 Location: {model_file}")
        
        # Kiểm tra kích thước file
        if model_file.exists():
            size_mb = model_file.stat().st_size / (1024 * 1024)
            print(f"📊 Size: {size_mb:.1f} MB")
            
            if size_mb < 100:
                print("⚠️  File có vẻ nhỏ, có thể tải chưa hoàn chỉnh")
            else:
                print("✅ File size OK")
        
        print()
        print("🚀 Bây giờ bạn có thể chạy:")
        print("   python run_img2img_artistic.py")
        
        return 0
    else:
        print()
        print("❌ Tải model thất bại!")
        print("💡 Bạn có thể:")
        print("   1. Thử lại script này")
        print("   2. Tải thủ công từ https://huggingface.co/runwayml/stable-diffusion-v1-5")
        print(f"   3. Đặt file model.ckpt vào: {model_dir}")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
