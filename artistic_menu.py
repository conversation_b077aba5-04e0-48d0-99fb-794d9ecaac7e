#!/usr/bin/env python3
"""
Menu ch<PERSON><PERSON> cho Artistic House Converter
"""

import os
import sys
import subprocess

def show_menu():
    """Hiển thị menu chính"""
    print("🎨 ARTISTIC HOUSE CONVERTER")
    print("=" * 50)
    print("Chuyển đổi ảnh nhà thành phong cách nghệ thuật")
    print()
    print("📋 MENU:")
    print("1. 🚀 Demo nhanh (1 style)")
    print("2. 🎭 Tạo nhiều style cùng lúc")
    print("3. 💪 Test các strength khác nhau")
    print("4. 🔧 Sử dụng optimized script (cần model lớn)")
    print("5. 📊 Xem kết quả đã tạo")
    print("6. ❓ Hướng dẫn")
    print("0. 🚪 Thoát")
    print()

def run_quick_demo():
    """Chạy demo nhanh"""
    print("🚀 DEMO NHANH")
    print("-" * 30)
    print("Chọn style:")
    print("1. Fantasy (phong cách cổ tích)")
    print("2. Artistic (phong cách hội họa)")
    print("3. Sketch (phong cách phác thảo)")
    print("4. Modern (phong cách hiện đại)")
    
    choice = input("\nChọn style (1-4): ").strip()
    
    styles = {"1": "fantasy", "2": "artistic", "3": "sketch", "4": "modern"}
    style = styles.get(choice, "fantasy")
    
    strength = input("Nhập strength (0.1-1.0, mặc định 0.7): ").strip()
    if not strength:
        strength = "0.7"
    
    cmd = [
        "python", "quick_artistic_demo.py",
        "--style", style,
        "--strength", strength
    ]
    
    print(f"\n🎨 Tạo ảnh {style} với strength {strength}...")
    subprocess.run(cmd)

def run_batch_conversion():
    """Chạy batch conversion"""
    print("🎭 TẠO NHIỀU STYLE")
    print("-" * 30)
    print("Sẽ tạo 5 phiên bản khác nhau:")
    print("- Fantasy (strength 0.6 và 0.8)")
    print("- Artistic (strength 0.7)")
    print("- Sketch (strength 0.8)")
    print("- Modern (strength 0.5)")
    print()
    
    confirm = input("Tiếp tục? (y/n): ").strip().lower()
    if confirm == 'y':
        subprocess.run(["python", "batch_artistic_converter.py"])

def test_strengths():
    """Test các strength khác nhau"""
    print("💪 TEST STRENGTH")
    print("-" * 30)
    
    style = input("Chọn style (fantasy/artistic/sketch/modern): ").strip()
    if not style:
        style = "fantasy"
    
    print(f"\nSẽ tạo {style} với các strength: 0.3, 0.5, 0.7, 0.9")
    
    confirm = input("Tiếp tục? (y/n): ").strip().lower()
    if confirm == 'y':
        for strength in [0.3, 0.5, 0.7, 0.9]:
            output = f"{style}_strength_{strength}.jpg"
            cmd = [
                "python", "quick_artistic_demo.py",
                "--style", style,
                "--strength", str(strength),
                "--output", output
            ]
            print(f"\n🎨 Tạo với strength {strength}...")
            subprocess.run(cmd)

def run_optimized():
    """Chạy optimized script"""
    print("🔧 OPTIMIZED SCRIPT")
    print("-" * 30)
    print("Sử dụng script optimized từ GitHub repo")
    print("Cần model checkpoint lớn (7GB)")
    print()
    
    # Kiểm tra model
    model_path = "home-improvement/stable-diffusion-optimized/models/ldm/stable-diffusion-v1/model.ckpt"
    if os.path.exists(model_path):
        size_mb = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ Model có sẵn ({size_mb:.0f} MB)")
        
        confirm = input("Chạy optimized script? (y/n): ").strip().lower()
        if confirm == 'y':
            subprocess.run(["python", "run_img2img_artistic.py"])
    else:
        print("❌ Model chưa có")
        print("Chạy: python download_model.py để tải model")

def show_results():
    """Hiển thị kết quả"""
    print("📊 KẾT QUẢ ĐÃ TẠO")
    print("-" * 30)
    
    # Tìm các file ảnh output
    output_files = []
    
    # Tìm trong thư mục hiện tại
    for file in os.listdir("."):
        if file.endswith((".jpg", ".png")) and "artistic" in file.lower():
            output_files.append(file)
    
    # Tìm trong thư mục artistic_outputs
    if os.path.exists("artistic_outputs"):
        for file in os.listdir("artistic_outputs"):
            if file.endswith((".jpg", ".png")):
                output_files.append(f"artistic_outputs/{file}")
    
    if output_files:
        print(f"Tìm thấy {len(output_files)} file:")
        for i, file in enumerate(output_files, 1):
            if os.path.exists(file):
                size_mb = os.path.getsize(file) / (1024 * 1024)
                print(f"   {i}. {file} ({size_mb:.2f} MB)")
    else:
        print("Chưa có file nào được tạo")

def show_help():
    """Hiển thị hướng dẫn"""
    print("❓ HƯỚNG DẪN")
    print("-" * 30)
    print("🎯 MỤC TIÊU:")
    print("   Chuyển đổi ảnh nhà thành phong cách nghệ thuật")
    print("   như ảnh vẽ, illustration, concept art")
    print()
    print("🎭 CÁC STYLE:")
    print("   • Fantasy: Phong cách cổ tích, magical")
    print("   • Artistic: Phong cách hội họa, watercolor")
    print("   • Sketch: Phong cách phác thảo, lineart")
    print("   • Modern: Phong cách hiện đại, concept art")
    print()
    print("💪 STRENGTH:")
    print("   • 0.1-0.4: Giữ gần như nguyên ảnh gốc")
    print("   • 0.5-0.7: Cân bằng giữa gốc và style mới")
    print("   • 0.8-1.0: Thay đổi mạnh, ít giống ảnh gốc")
    print()
    print("⚡ HIỆU SUẤT:")
    print("   • CPU: Chậm (5-10 phút/ảnh)")
    print("   • GPU: Nhanh (30s-2 phút/ảnh)")
    print()
    print("📁 FILE INPUT:")
    print("   • Ảnh gốc: home-improvement/z6691178742842_...jpg")
    print("   • Kích thước tối ưu: 512x512 hoặc tương tự")

def main():
    """Hàm chính"""
    
    # Kiểm tra file input
    input_file = "home-improvement/z6691178742842_2638d535a4bec2d9380236f46ddcdba4.jpg"
    if not os.path.exists(input_file):
        print("❌ Không tìm thấy file ảnh input!")
        print(f"   Cần file: {input_file}")
        return 1
    
    while True:
        show_menu()
        choice = input("Chọn (0-6): ").strip()
        
        if choice == "0":
            print("👋 Tạm biệt!")
            break
        elif choice == "1":
            run_quick_demo()
        elif choice == "2":
            run_batch_conversion()
        elif choice == "3":
            test_strengths()
        elif choice == "4":
            run_optimized()
        elif choice == "5":
            show_results()
        elif choice == "6":
            show_help()
        else:
            print("❌ Lựa chọn không hợp lệ!")
        
        input("\nNhấn Enter để tiếp tục...")
        print("\n" + "="*60 + "\n")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
